// Copyright Epic Games, Inc. All Rights Reserved.

#include "Pipeline/PipelineProcess.h"
#include "Pipeline/Log.h"
#include "Pipeline/PipelineData.h"
#include "Pipeline/Node.h"
#include "MetaHumanTrace.h"
#include "Async/Async.h"
#include "Internationalization/TextLocalizationResource.h"

#if WITH_EDITOR
#include "Editor.h"
#endif

#include UE_INLINE_GENERATED_CPP_BY_NAME(PipelineProcess)

UMetaHumanPipelineProcess::UMetaHumanPipelineProcess()
{
}

void UMetaHumanPipelineProcess::BeginDestroy()
{
	Stop();

	if (MessageTimerHandle.IsValid())
	{
#if WITH_EDITOR
		GEditor->GetTimerManager()->ClearTimer(MessageTimerHandle);
#endif
	}

	Super::BeginDestroy();
}

void UMetaHumanPipelineProcess::Start(const TArray<TSharedPtr<UE::MetaHuman::Pipeline::FNode>>& InNodes,
	const TArray<UE::MetaHuman::Pipeline::FConnection>& InConnections,
	const UE::MetaHuman::Pipeline::FPipelineRunParameters& InPipelineRunParameters)
{
	PipelineRunParameters = InPipelineRunParameters;

	bClearMessageQueue = false;

	if (Process)
	{
		TSharedPtr<UE::MetaHuman::Pipeline::FPipelineData> PipelineData = MakeShared<UE::MetaHuman::Pipeline::FPipelineData>();

		PipelineData->SetExitStatus(UE::MetaHuman::Pipeline::EPipelineExitStatus::AlreadyRunning);
		PipelineData->SetErrorMessage("Pipeline already running");

		PipelineRunParameters.GetOnProcessComplete().Broadcast(PipelineData);

		return;
	}

	if (InPipelineRunParameters.GetRestrictStartingToGameThread() && !IsInGameThread())
	{
		TSharedPtr<UE::MetaHuman::Pipeline::FPipelineData> PipelineData = MakeShared<UE::MetaHuman::Pipeline::FPipelineData>();

		PipelineData->SetExitStatus(UE::MetaHuman::Pipeline::EPipelineExitStatus::NotInGameThread);
		PipelineData->SetErrorMessage("Pipeline must be started from game thread");

		PipelineRunParameters.GetOnProcessComplete().Broadcast(PipelineData);

		return;
	}

	UE::MetaHuman::Pipeline::FFrameComplete OnFrameCompleteInternal;
	UE::MetaHuman::Pipeline::FProcessComplete OnProcessCompleteInternal;

	OnFrameCompleteInternal.AddUObject(this, &UMetaHumanPipelineProcess::FrameComplete);
	OnProcessCompleteInternal.AddUObject(this, &UMetaHumanPipelineProcess::ProcessComplete);

	UE::MetaHuman::Pipeline::FPipelineRunParameters PipelineRunParametersInternal = PipelineRunParameters;
	PipelineRunParametersInternal.SetOnFrameComplete(OnFrameCompleteInternal);
	PipelineRunParametersInternal.SetOnProcessComplete(OnProcessCompleteInternal);

	ENamedThreads::Type CallingThread;

	if (PipelineRunParameters.GetMode() == UE::MetaHuman::Pipeline::EPipelineMode::PushSync || 
		PipelineRunParameters.GetMode() == UE::MetaHuman::Pipeline::EPipelineMode::PushSyncNodes)
	{
		CallingThread = (ENamedThreads::Type)-1;
	}
	else
	{
		CallingThread = ENamedThreads::GameThread;
	}

	Process = new UE::Geometry::FAsyncTaskExecuterWithAbort<UE::MetaHuman::Pipeline::FPipelineProcess>(InNodes, InConnections, PipelineRunParametersInternal, CallingThread);
	Process->GetTask().TaskWrapper = Process;

	TOptional<FString> GPU = PipelineRunParameters.GetGpuToUse();

	if (GPU.IsSet())
	{
		if (GPU.GetValue().IsEmpty())
		{
			UE_LOG(LogMetaHumanPipeline, Display, TEXT("Pipeline using unspecified GPU"));
		}
		else
		{
			UE_LOG(LogMetaHumanPipeline, Display, TEXT("Pipeline using GPU '%s'"), *GPU.GetValue());
		}
	}

	if (PipelineRunParameters.GetMode() == UE::MetaHuman::Pipeline::EPipelineMode::PushSync ||
		PipelineRunParameters.GetMode() == UE::MetaHuman::Pipeline::EPipelineMode::PushSyncNodes)
	{
		Process->StartSynchronousTask();

		delete Process;
		Process = nullptr;
	}
	else
	{
		Process->StartBackgroundTask();
	}
}

bool UMetaHumanPipelineProcess::IsRunning() const
{
	return Process != nullptr;
}

void UMetaHumanPipelineProcess::Stop(bool bInClearMessageQueue)
{
	bClearMessageQueue = bInClearMessageQueue;

	if (Process)
	{
		Process->bAbort = true;

		// Temporary fix for the demo. 
		// 
		// Calling EnsureCompletion here will cause Stop() to block until the processing is complete
		// but it does not wait until the events generated by that processing have been delivered.
		// Frame complete and process complete events could still be delivered to the client process
		// after Stop() has returned. So, the blocking is more "stop processing" rather than a full
		// "stop all activity".
		// 
		// But the immediate problem this fix solves is the hyprsense processing crashing if running 
		// when the editor is shut down. And for that this pseudo-blocking is sufficient.
		// 
		// The correct fix IMHO for this is for the hyprsense node to ensure anything it uses is kept
		// alive for the duration of the nodes lifetime. Currently the node stores UNeuralNetwork* but
		// there is nothing to prevent the UNeuralNetwork* going out of scope while still being used 
		// by the hyprsense node. The garbage collection does not know the hyprsense node is using the
		// neural network. I thought the shutdown problem could be fixed by something like the hyprsense
		// node calling UNeuralNetwork::AddToRoot() in its SetTrackers and UNeuralNetwork::RemoveFromRoot()
		// in its destructor so the NN's were kept alive as needed. But that did not work, looks like something
		// internal to the NN has been destroyed even though the NN is still using it. But what exactly 
		// that is and the whole order of destruction on shutdown is too complex to look into now.
		if (PipelineRunParameters.GetMode() != UE::MetaHuman::Pipeline::EPipelineMode::PushSyncNodes &&
			PipelineRunParameters.GetMode() != UE::MetaHuman::Pipeline::EPipelineMode::PushSync)
		{
			Process->EnsureCompletion();
		}
	}
}

void UMetaHumanPipelineProcess::PipelineNowInvalid()
{
	UE::MetaHuman::Pipeline::FPipelineRunParameters InvalidatedPipelineRunParameters;
	InvalidatedPipelineRunParameters.SetMode(PipelineRunParameters.GetMode());

	PipelineRunParameters = InvalidatedPipelineRunParameters;
}

// The FrameComplete and ProcessComplete functions below are called as part of an Async task.
// Care needs to be taken that the OnFrameComplete and OnProcessComplete delegates they call 
// do not occur when the editor is in a non-ticking state, like when a modal dialog is shown.
// If they do, and the delegate itself trys to open a modal dialog, then this can interfere 
// with the completion of the Async task and lead to the editor hanging. See MH-11273.
// The quick fix for this is to not execute the OnFrameComplete and OnProcessComplete delegates
// immediately but for them to occur on next editor tick. However, this change is not ideal since
// it introduces an Editor dependency to the pipeline module. This is not an problem right now,
// but would impact the pipeline being used in-game or in a runtime module like livelink. 
// The correct solution here I feel is to have 2 pipeline classes in separate modules - one for
// runtime use and one for use in editor. The runtime pipeline would be the pipeline that existed
// prior to the MH-11273 changes where the OnFrameComplete and OnProcessComplete were called immediately.
// The editor pipeline would then be a minimal class on top that inherits from the runtime pipeline
// and overrides the FrameComplete and ProcessComplete functions so they work like below.

void UMetaHumanPipelineProcess::AddToMessageQueue(bool bInIsFrameComplete, TSharedPtr<UE::MetaHuman::Pipeline::FPipelineData> InPipelineData)
{
	MessageQueue.Add({ bInIsFrameComplete, InPipelineData });

	if (!MessageTimerHandle.IsValid())
	{
#if WITH_EDITOR
		MessageTimerHandle = GEditor->GetTimerManager()->SetTimerForNextTick([this]()
		{
			DispatchMessageQueue();
		});
#endif
	}
}

void UMetaHumanPipelineProcess::DispatchMessageQueue()
{
	check(!MessageQueue.IsEmpty());

	// Clear all the frame complete messages from the queue. If a lot of frame complete messages have 
	// been sent out and built up in the queue at the point of canceling processing, this ensures that
	// no more frame complete messages get broadcast and updates stop immediately.
	if (bClearMessageQueue)
	{
		MessageQueue.RemoveAll([](const TPair<bool, TSharedPtr<UE::MetaHuman::Pipeline::FPipelineData>>& Message) {
			return Message.Key;
		});

		bClearMessageQueue = false;
		
		if (MessageQueue.IsEmpty())
		{
			MessageTimerHandle.Invalidate();
			return;
		}
	}

	while (!MessageQueue.IsEmpty())
	{
		TPair<bool, TSharedPtr<UE::MetaHuman::Pipeline::FPipelineData>> Message = MessageQueue[0];
		MessageQueue.RemoveAt(0);

		if (Message.Key)
		{
			PipelineRunParameters.GetOnFrameComplete().Broadcast(Message.Value);
		}
		else
		{
			PipelineRunParameters.GetOnProcessComplete().Broadcast(Message.Value);
		}
	}

	MessageTimerHandle.Invalidate();
}

void UMetaHumanPipelineProcess::FrameComplete(TSharedPtr<UE::MetaHuman::Pipeline::FPipelineData> InPipelineData)
{
	bool bIsAsync = PipelineRunParameters.GetMode() == UE::MetaHuman::Pipeline::EPipelineMode::PushAsync ||
					PipelineRunParameters.GetMode() == UE::MetaHuman::Pipeline::EPipelineMode::PushAsyncNodes;

	if (IsInGameThread() && bIsAsync)
	{
		AddToMessageQueue(true, InPipelineData);
	}
	else
	{
		PipelineRunParameters.GetOnFrameComplete().Broadcast(InPipelineData);
	}
}

void UMetaHumanPipelineProcess::ProcessComplete(TSharedPtr<UE::MetaHuman::Pipeline::FPipelineData> InPipelineData)
{
	bool bIsAsync = PipelineRunParameters.GetMode() == UE::MetaHuman::Pipeline::EPipelineMode::PushAsync ||
					PipelineRunParameters.GetMode() == UE::MetaHuman::Pipeline::EPipelineMode::PushAsyncNodes;

	if (IsInGameThread() && bIsAsync)
	{
		AddToMessageQueue(false, InPipelineData);
	}
	else
	{
		PipelineRunParameters.GetOnProcessComplete().Broadcast(InPipelineData);
	}

	if (PipelineRunParameters.GetMode() != UE::MetaHuman::Pipeline::EPipelineMode::PushSync)
	{
		Process = nullptr;
	}
}



namespace UE::MetaHuman::Pipeline
{

FNodeInternal::FNodeInternal(const FString& InName) : FNode("", InName)
{
}



FPipelineDataQueue::FPipelineDataQueue(const TNodePair &InNodePair) : MaxSize(InNodePair.Value->QueueSize)
{
	Queue.Reserve(MaxSize);
}

FPipelineDataQueue::FPipelineDataQueue(const FPipelineDataQueue &InOther)
{
	FScopeLock ScopeLock(&InOther.Mutex);

	MaxSize = InOther.MaxSize;
	Queue = InOther.Queue;
}

bool FPipelineDataQueue::IsEmpty() const
{
	FScopeLock ScopeLock(&Mutex);

	return Queue.IsEmpty();
}

bool FPipelineDataQueue::CanPush() const
{
	FScopeLock ScopeLock(&Mutex);

	return Queue.Num() < MaxSize;
}

void FPipelineDataQueue::Push(const TSharedPtr<FPipelineData>& InPipelineData)
{
	FScopeLock ScopeLock(&Mutex);

	Queue.Add(InPipelineData);
}

TSharedPtr<FPipelineData> FPipelineDataQueue::Pop()
{
	FScopeLock ScopeLock(&Mutex);

	TSharedPtr<FPipelineData> PipelineData = Queue[0];

	Queue.RemoveAt(0);

	return PipelineData;
}

void FPipelineDataQueue::RemoveFramesAbove(int32 InFrameNumber)
{
	FScopeLock ScopeLock(&Mutex);

	for (int32 Index = Queue.Num() - 1; Index >= 0; --Index)
	{
		if (Queue[Index]->GetFrameNumber() >= InFrameNumber)
		{
			Queue.RemoveAt(Index);
		}
	}
}



FPipelineProcess::FPipelineProcess(const TArray<TSharedPtr<FNode>>& InNodes, const TArray<FConnection>& InConnections,
	const FPipelineRunParameters& InPipelineRunParameters, ENamedThreads::Type InCallingThread)
	: Nodes(InNodes), Connections(InConnections), PipelineRunParameters(InPipelineRunParameters), CallingThread(InCallingThread)
{
}

void FPipelineProcess::DoWork()
{
	MHA_CPUPROFILER_EVENT_SCOPE(FPipelineProcess::DoWork);

#if WITH_EDITOR
	// Set the log verbosity level
	ELogVerbosity::Type Verbosity = LogMetaHumanPipeline.GetVerbosity();
	LogMetaHumanPipeline.SetVerbosity(PipelineRunParameters.GetVerbosity());
#endif

	// Run pipeline
	RunPipeline();

#if WITH_EDITOR
	// Restore log verbosity level
	LogMetaHumanPipeline.SetVerbosity(Verbosity);
#endif

	// Cleanup - clear all pins
	for (TSharedPtr<FNode>& Node : Nodes)
	{
		for (FPin& Pin : Node->Pins)
		{
			Pin.Address = "";
		}
	}

	// Cleanup - ensure we are not holding onto any shared pointers
	Nodes.Reset();
	Connections.Reset();
	InternalSourceNode.Reset();
	InternalSyncNode.Reset();
	UpstreamNodes.Reset();
	DownstreamNodes.Reset();
	Queue.Reset();

	// Processing complete, send results
	if (CallingThread == -1)
	{
		PipelineRunParameters.GetOnProcessComplete().Broadcast(ExitPipelineData);
	}
	else
	{
		AsyncTask(CallingThread, [this]() {
			TaskWrapper->EnsureCompletion();
			PipelineRunParameters.GetOnProcessComplete().Broadcast(ExitPipelineData);
			delete TaskWrapper;
		});
	}
}

void FPipelineProcess::StartNode(const TSharedPtr<FNode>& InNode)
{
	TSharedPtr<FPipelineData> PipelineData = MakeShared<FPipelineData>();
	PipelineData->SetUseGPU(PipelineRunParameters.GetGpuToUse().Get(TEXT("")));

	if (!InNode->Start(PipelineData))
	{
		FScopeLock ScopeLock(&ExitMutex);

		if (!ExitPipelineData)
		{
			UE_LOG(LogMetaHumanPipeline, Error, TEXT("   Start error in node \"%s\" - Code %i, Message \"%s\""), *InNode->Name, PipelineData->GetErrorNodeCode(), *PipelineData->GetErrorNodeMessage());

			PipelineData->SetExitStatus(EPipelineExitStatus::StartError);
			PipelineData->SetErrorMessage("Node start error");
			PipelineData->SetErrorNodeName(InNode->Name);

			ExitPipelineData = PipelineData;

			ExitFrame = FPipelineProcess::ExitedOnStart;
			bIsProducing = false;
			bIsRunning = false;
		}
	}
}

bool FPipelineProcess::ProcessNode(const TSharedPtr<FNode>& InNode)
{
	// Is this node ready to process? There needs to be data in the queue from all upstream nodes, and
	// sufficient room to add data in the queue to all downstream nodes

	bool bIsInputReady = true;
	bool bIsOutputReady = true;

	for (const TSharedPtr<FNode>& UpstreamNode : UpstreamNodes[InNode])
	{
		if (Queue[TNodePair(UpstreamNode, InNode)].IsEmpty())
		{
			bIsInputReady = false;
			break;
		}
	}

	for (const TSharedPtr<FNode>& DownstreamNode : DownstreamNodes[InNode])
	{
		if (!Queue[TNodePair(InNode, DownstreamNode)].CanPush())
		{
			bIsOutputReady = false;
			break;
		}
	}

	TSharedPtr<FPipelineData> PipelineData = nullptr;
	bool bPipelineDataOk = true;

	if (bIsInputReady && bIsOutputReady) // Process a frame
	{
		if (InNode == InternalSourceNode) // The internal source node create the new frame
		{
			if (bIsProducing)
			{
				PipelineData = MakeShared<FPipelineData>();
				PipelineData->SetMarkerStartTime("Frame");
				PipelineData->SetFrameNumber(Frame++);

				bPipelineDataOk = PipelineRunParameters.GetEndFrame() != PipelineData->GetFrameNumber();

				UE_LOG(LogMetaHumanPipeline, VeryVerbose, TEXT("   %i:%s"), PipelineData->GetFrameNumber(), *InNode->Name);

				if (PipelineRunParameters.GetCheckProcessingSpeed())
				{
					int32 FramesGenerated = Frame - PipelineRunParameters.GetStartFrame();
					if (FramesGenerated % 100 == 0)
					{
						double ElapsedTime = FPlatformTime::Seconds() - StartTime;
						if (ElapsedTime > 1.0f)
						{
							if (FramesGenerated / ElapsedTime > FPipelineProcess::MaxRate)
							{
								PipelineData->SetExitStatus(EPipelineExitStatus::TooFast);
								PipelineData->SetErrorMessage("Pipeline running too fast");

								bPipelineDataOk = false;
							}
						}
					}
				}
			}
		}
		else
		{
			// Remove data from queues of all upstream nodes - the FPipelineData of each must be the same
			for (const TSharedPtr<FNode>& UpstreamNode : UpstreamNodes[InNode])
			{
				TSharedPtr<FPipelineData> QueuePipelineData = Queue[TNodePair(UpstreamNode, InNode)].Pop();

				checkf(!PipelineData || QueuePipelineData == PipelineData, TEXT("Queue out of sync"));

				PipelineData = QueuePipelineData;
			}

			UE_LOG(LogMetaHumanPipeline, VeryVerbose, TEXT("   %i:%s"), PipelineData->GetFrameNumber(), *InNode->Name);

			if (InNode == InternalSyncNode) // The internal sync node signifies the complete processing of a frame - send notification
			{
				if (PipelineData->GetEndFrameMarker()) // The "endframe" marker signifies the end of processing for all nodes 
				{
					if (PipelineData->GetErrorNodeCode() != -1)
					{
						FScopeLock ScopeLock(&ExitMutex);

						ExitPipelineData = PipelineData;
					}

					bIsRunning = false;
				}
				else
				{
					FFrameComplete NonMemberOnFrameComplete = PipelineRunParameters.GetOnFrameComplete();

					UE_LOG(LogMetaHumanPipeline, Verbose, TEXT("Processed Frame %d"), PipelineData->GetFrameNumber());

					if (CallingThread == -1)
					{
						NonMemberOnFrameComplete.Broadcast(PipelineData);
					}
					else
					{
						PipelineData->SetMarkerEndTime("Frame");

						AsyncTask(CallingThread, [NonMemberOnFrameComplete, PipelineData]() {
							NonMemberOnFrameComplete.Broadcast(PipelineData);
						});
					}
				}
			}
			else
			{
				// Ensure we are not processing a frame greater than the exit frame, which is the frame that caused the pipeline to terminate.
				// Whether we process the exit frame itself is an option for the node

				bool bIsValidFrame = true;
				bool bIsExitFrame = false;
				bool bIsPostExitFrame = false;

				if (!bIsProducing)
				{
					FScopeLock ScopeLock(&ExitMutex);
					int32 FrameNumber = PipelineData->GetFrameNumber();
					bIsValidFrame = FrameNumber < ExitFrame;
					bIsExitFrame = FrameNumber == ExitFrame;
					bIsPostExitFrame = FrameNumber > ExitFrame;
				}

				if (bIsValidFrame || (bIsExitFrame && InNode->ProcessesExitFrame()))
				{
					FString TraceString = FString::Printf(TEXT("%i"), PipelineData->GetFrameNumber());
					MHA_CPUPROFILER_EVENT_SCOPE_TEXT(*TraceString);

					PipelineData->SetMarkerStartTime(InNode->Name);
					bPipelineDataOk = InNode->Process(PipelineData);
					PipelineData->SetMarkerEndTime(InNode->Name);
				}
				else if (bIsPostExitFrame)
				{
					PipelineData->SetDropFrame(true);
				}
			}
		}
	}
	else if (bIsOutputReady && InNode->RequiresIdle()) // No input to process a frame, but node can still generate data to push down the pipeline in the idle function
	{
		PipelineData = MakeShared<FPipelineData>();

		bPipelineDataOk = InNode->Idle(PipelineData);
	}

	if (PipelineData) // Data to pass down pipeline
	{
		if (!bPipelineDataOk) // Process/Idle failed
		{
			FScopeLock ScopeLock(&ExitMutex);

			if (ExitFrame == FPipelineProcess::NotExited)
			{
				// Flag the pipeline data as being the end frame.
				// Stop the internal source node producing new frames
				// Note this exit frame (dont process any data whose frame number is greater than
				// this value - such data may already be queued for processing in other nodes) 
				PipelineData->SetEndFrameMarker(true);

				bIsProducing = false;
				ExitFrame = PipelineData->GetFrameNumber();

				// If the node did not set a "ErrorNodeCode" (its at the default -1) then this
				// indicates the node request the pipeline to terminate rather than a true error.
				if (PipelineData->GetErrorNodeCode() == -1)
				{
					UE_LOG(LogMetaHumanPipeline, VeryVerbose, TEXT("    Terminate issued by node \"%s\" on frame %i"), *InNode->Name, PipelineData->GetFrameNumber());
				}
				else
				{
					UE_LOG(LogMetaHumanPipeline, Error, TEXT("    Process error in node \"%s\" on frame %i - Code %i, Message \"%s\""), *InNode->Name, PipelineData->GetFrameNumber(), PipelineData->GetErrorNodeCode(), *PipelineData->GetErrorNodeMessage());

					PipelineData->SetExitStatus(EPipelineExitStatus::ProcessError);
					PipelineData->SetErrorMessage("Node process error");
					PipelineData->SetErrorNodeName(InNode->Name);
				}
			}
		}

		if (!PipelineData->GetDropFrame()) // Dont pass on data marked as a drop frame
		{
			// Pass data onto the queue for all downstream nodes
			for (const TSharedPtr<FNode>& DownstreamNode : DownstreamNodes[InNode])
			{
				Queue[TNodePair(InNode, DownstreamNode)].Push(PipelineData);
			}
		}
	}

	return (PipelineData && !PipelineData->GetDropFrame());
}

void FPipelineProcess::EndNode(const TSharedPtr<FNode>& InNode)
{
	TSharedPtr<FPipelineData> PipelineData = MakeShared<FPipelineData>();
	if (!InNode->End(PipelineData))
	{
		FScopeLock ScopeLock(&ExitMutex);

		if (!ExitPipelineData)
		{
			UE_LOG(LogMetaHumanPipeline, Error, TEXT("   End error in node \"%s\" - Code %i, Message \"%s\""), *InNode->Name, PipelineData->GetErrorNodeCode(), *PipelineData->GetErrorNodeMessage());

			PipelineData->SetExitStatus(EPipelineExitStatus::EndError);
			PipelineData->SetErrorMessage("Node end error");
			PipelineData->SetErrorNodeName(InNode->Name);

			ExitPipelineData = PipelineData;

			ExitFrame = FPipelineProcess::ExitedOnEnd;
			bIsProducing = false;
			bIsRunning = false;
		}
	}
}



// This function sets up, and processes, the pipeline.

void FPipelineProcess::RunPipeline()
{
	bIsRunning = true;
	bIsProducing = true;

	ExitFrame = FPipelineProcess::NotExited;
	ExitPipelineData = nullptr;

	Frame = PipelineRunParameters.GetStartFrame();

	StartTime = FPlatformTime::Seconds();

	{
		MHA_CPUPROFILER_EVENT_SCOPE_STR("Setup");

		ConnectPins(); // Connect the low level pins of each node given the high level node connections passed in

		if (!ExitPipelineData) // Connecting pins did not lead to an error
		{
			// Create a pair of internal nodes to act as the single top and bottom of the node graph (makes processing logic easier)
			InternalSourceNode = MakeShared<FNodeInternal>("InternalSource");
			InternalSyncNode = MakeShared<FNodeInternal>("InternalSync");

			// Create inter-node lookups, ie for each node a list of all upstream and downstream nodes plus the message queue for each node pair
			MakeNodeConnectionLookupsAndQueues();

			// Add internal nodes to list of nodes to process. These nodes being processed represent a new frame being started (InternalSource)
			// and a frame being fully processed (InternalSync)
			Nodes.Insert(InternalSourceNode, 0);
			Nodes.Add(InternalSyncNode);

			// Optionally mix up the nodes. Without this, for single threaded runs, nodes are processed in the order they were created 
			// and added to the pipeline, which is typically the order in which they need to be processed. This is desirable in certain
			// situations but not in other since by mixing up the order of nodes we ensure creation order != processing order which 
			// helps highlight any problems in the queuing.
			if (PipelineRunParameters.GetProcessNodesInRandomOrder())
			{
				Nodes.Sort([](const TSharedPtr<FNode>& Item1, const TSharedPtr<FNode>& Item2) {
					return FTextLocalizationResource::HashString(Item1->Name) < FTextLocalizationResource::HashString(Item2->Name);
				});
			}
		}
	}

	if (!ExitPipelineData)
	{
		for (const TSharedPtr<FNode>& Node : Nodes)
		{
			Node->bAbort = &TaskWrapper->bAbort;
		}

		// Process the pipeline
		if (PipelineRunParameters.GetMode() == EPipelineMode::PushSync || PipelineRunParameters.GetMode() == EPipelineMode::PushAsync)
		{
			PushSingleThreaded();
		}
		else if (PipelineRunParameters.GetMode() == EPipelineMode::PushSyncNodes || PipelineRunParameters.GetMode() == EPipelineMode::PushAsyncNodes)
		{
			PushThreadPerNode();
		}
		else
		{
			checkf(false, TEXT("Unhandled mode"));
		}

		for (const TSharedPtr<FNode>& Node : Nodes)
		{
			Node->bAbort = nullptr;
		}
	}

	if (!ExitPipelineData)
	{
		ExitPipelineData = MakeShared<FPipelineData>();
		ExitPipelineData->SetExitStatus(IsAborted() ? EPipelineExitStatus::Aborted : EPipelineExitStatus::Ok);
	}
}



// Get an ordered list of upstream nodes. In general there will be multiple lists of upstream nodes, 
// each called a path, due to the node potentially having more than one input 

static TArray<TArray<TPair<TSharedPtr<FNode>, int32>>> GetNodePaths(const TArray<FConnection>& InConnections, const TSharedPtr<FNode>& InNode, int32 InGroup, const TArray<TSharedPtr<FNode>>& InVisitedNodes, bool& bOutIsLoopedConnection)
{
	TArray<TArray<TPair<TSharedPtr<FNode>, int32>>> NodePaths;

	if (InVisitedNodes.Contains(InNode))
	{
		bOutIsLoopedConnection = true;
	}
	else
	{
		for (const FConnection& Connection : InConnections)
		{
			if (Connection.To == InNode && (InGroup == -1 || Connection.ToGroup == InGroup))
			{
				TArray<TSharedPtr<FNode>> VisitedNodes = InVisitedNodes;
				VisitedNodes.Add(InNode);
				TArray<TArray<TPair<TSharedPtr<FNode>, int32>>> ConnectionNodePaths = GetNodePaths(InConnections, Connection.From, -1, VisitedNodes, bOutIsLoopedConnection);

				if (ConnectionNodePaths.IsEmpty())
				{
					ConnectionNodePaths.Add(TArray<TPair<TSharedPtr<FNode>, int32>>());
				}

				for (TArray<TPair<TSharedPtr<FNode>, int32>>& Path : ConnectionNodePaths)
				{
					Path.Insert(TPair<TSharedPtr<FNode>, int32>(Connection.From, Connection.FromGroup), 0);
					NodePaths.Add(Path);
				}
			}
		}
	}

	return NodePaths;
}



// This function connects the inputs pins of each node to the output pins of other nodes.
// These connection are partially defined by from the high level node-to-node connections 
// passed in, and an "auto connection" strategy.
//
// By having the user define node-to-node, not pin-to-pin, connections we greatly reduce the number of
// connections they needs to specify. The pin-to-pin connections can be worked out
// automatically from the node-to-node connections the vast majority of the time. The rare times
// where the automatic connection fails leads to an error which the user can address by hand.
// 
// The auto connection strategy is, for input pin in each node, to walk up the node graph to find 
// the nearest upstream node that has an output pin of the same type. So a pin may be connected to a 
// pin in the immediate upstream node or a node several steps away.

void FPipelineProcess::ConnectPins()
{
	// Check nodes are well defined, eg have a unique name, not a reserved name etc
	// Create a unique address for output pins - this is "NodeName.PinName"

	TArray<FString> NodeNames;

	for (const TSharedPtr<FNode>& Node : Nodes)
	{
		if (Node->TypeName.IsEmpty())
		{
			ExitPipelineData = MakeShared<FPipelineData>();
			ExitPipelineData->SetExitStatus(EPipelineExitStatus::InvalidNodeTypeName);
			ExitPipelineData->SetErrorMessage(FString::Printf(TEXT("Invalid node type name \"%s\" in node \"%s\""), *Node->TypeName, *Node->Name));
		}

		if (Node->Name.IsEmpty() || Node->Name.Contains(".") || Node->Name == "Reserved" || Node->Name == "Frame")
		{
			ExitPipelineData = MakeShared<FPipelineData>();
			ExitPipelineData->SetExitStatus(EPipelineExitStatus::InvalidNodeName);
			ExitPipelineData->SetErrorMessage(FString::Printf(TEXT("Invalid node name \"%s\""), *Node->Name));
		}

		if (NodeNames.Contains(Node->Name))
		{
			ExitPipelineData = MakeShared<FPipelineData>();
			ExitPipelineData->SetExitStatus(EPipelineExitStatus::DuplicateNodeName);
			ExitPipelineData->SetErrorMessage(FString::Printf(TEXT("Duplicate node name \"%s\""), *Node->Name));
		}

		NodeNames.Add(Node->Name);

		TArray<FString> PinNames;

		for (FPin& Pin : Node->Pins)
		{
			if (Pin.Name.IsEmpty() || Pin.Name.Contains("."))
			{
				ExitPipelineData = MakeShared<FPipelineData>();
				ExitPipelineData->SetExitStatus(EPipelineExitStatus::InvalidPinName);
				ExitPipelineData->SetErrorMessage(FString::Printf(TEXT("Invalid pin name \"%s\" in node \"%s\""), *Pin.Name, *Node->Name));
			}

			if (PinNames.Contains(Pin.Name))
			{
				ExitPipelineData = MakeShared<FPipelineData>();
				ExitPipelineData->SetExitStatus(EPipelineExitStatus::DuplicatePinName);
				ExitPipelineData->SetErrorMessage(FString::Printf(TEXT("Duplicate pin name \"%s\" in node \"%s\""), *Pin.Name, *Node->Name));
			}

			PinNames.Add(Pin.Name);

			if (Pin.Direction == EPinDirection::Output)
			{
				Pin.Address = Node->Name + "." + Pin.Name;
			}
		}
	}

	// Connect each input pin to an output pin

	for (const FConnection& Connection : Connections)
	{
		if (!Connection.To || !Connection.From)
		{
			ExitPipelineData = MakeShared<FPipelineData>();
			ExitPipelineData->SetExitStatus(EPipelineExitStatus::InvalidConnection);
			ExitPipelineData->SetErrorMessage("Connection has null nodes");
		}

		for (FPin& InputPin : Connection.To->Pins)
		{
			if (InputPin.Direction == EPinDirection::Input && InputPin.Group == Connection.ToGroup)
			{
				// Get an ordered list of upstream nodes. In general there will be multiple lists of upstream nodes, 
				// each called a path, due to the node potentially having more than one input 
				bool bIsLoopedConnection = false;
				TArray<TSharedPtr<FNode>> VisitedNodes;
				TArray<TArray<TPair<TSharedPtr<FNode>, int32>>> NodePaths = GetNodePaths(Connections, Connection.To, Connection.ToGroup, VisitedNodes, bIsLoopedConnection);

				if (bIsLoopedConnection) // Node connections must not contain loops
				{
					ExitPipelineData = MakeShared<FPipelineData>();
					ExitPipelineData->SetExitStatus(EPipelineExitStatus::LoopConnection);
					ExitPipelineData->SetErrorMessage(FString::Printf(TEXT("Loop connections to pin \"%s\" in node \"%s\""), *InputPin.Name, *Connection.To->Name));
				}

				// Walk up each path of nodes. For each path, look for a valid pin connection.
				// Each path needs to be checked, ie dont break after the a valid pin connection is found.
				// Different paths may result in the same pin connection being made, but if different paths
				// result in different pin connection then we have an ambiguous situation (the auto connection
				// logic is not sufficient, and we need user to intervene).
				for (const TArray<TPair<TSharedPtr<FNode>, int32>>& Path : NodePaths)
				{
					bool bIsPathConnected = false;

					for (const TPair<TSharedPtr<FNode>, int32>& NodeAndPinGroup : Path)
					{
						for (const FPin& OutputPin : NodeAndPinGroup.Key->Pins)
						{
							if (OutputPin.Direction == EPinDirection::Output && OutputPin.Type == InputPin.Type && OutputPin.Group == NodeAndPinGroup.Value)
							{
								bIsPathConnected = true;

								if (InputPin.Address.IsEmpty())
								{
									InputPin.Address = OutputPin.Address;
								}
								else if (InputPin.Address != OutputPin.Address)
								{
									ExitPipelineData = MakeShared<FPipelineData>();
									ExitPipelineData->SetExitStatus(EPipelineExitStatus::AmbiguousConnection);
									ExitPipelineData->SetErrorMessage(FString::Printf(TEXT("Ambiguous connections to pin \"%s\" in node \"%s\""), *InputPin.Name, *Connection.To->Name));
								}

								break;
							}
						}

						if (bIsPathConnected)
						{
							break;
						}
					}
				}
			}
		}
	}

	// Ensure all input pins are connected - error if not
	for (const TSharedPtr<FNode>& Node : Nodes)
	{
		for (const FPin& Pin : Node->Pins)
		{
			if (Pin.Direction == EPinDirection::Input && Pin.Address.IsEmpty())
			{
				ExitPipelineData = MakeShared<FPipelineData>();
				ExitPipelineData->SetExitStatus(EPipelineExitStatus::Unconnected);
				ExitPipelineData->SetErrorMessage(FString::Printf(TEXT("Unconnected pin \"%s\" in node \"%s\""), *Pin.Name, *Node->Name));
			}
		}
	}
}



// Create inter-node lookups, ie for each node a list of all upstream and downstream nodes plus the message queue for each node pair

void FPipelineProcess::MakeNodeConnectionLookupsAndQueues()
{
	DownstreamNodes.Add(InternalSourceNode);
	UpstreamNodes.Add(InternalSourceNode);

	DownstreamNodes.Add(InternalSyncNode);
	UpstreamNodes.Add(InternalSyncNode);

	if (Nodes.IsEmpty())
	{
		DownstreamNodes[InternalSourceNode].Add(InternalSyncNode);
		UpstreamNodes[InternalSyncNode].Add(InternalSourceNode);

		TNodePair NodePair(InternalSourceNode, InternalSyncNode);
		Queue.Add(NodePair, FPipelineDataQueue(NodePair));
	}
	else
	{
		for (const TSharedPtr<FNode>& Node : Nodes)
		{
			DownstreamNodes.Add(Node);
			UpstreamNodes.Add(Node);

			bool bIsExternalSourceNode = true;
			bool bIsExternalSyncNode = true;

			for (const FConnection& Connection : Connections)
			{
				if (Connection.To == Node)
				{
					bIsExternalSourceNode = false;
				}

				if (Connection.From == Node)
				{
					bIsExternalSyncNode = false;
				}
			}

			if (bIsExternalSourceNode) // Node is at the top of the graph - connect upstream to InternalSourceNode
			{
				DownstreamNodes[InternalSourceNode].Add(Node);
				UpstreamNodes[Node].Add(InternalSourceNode);

				TNodePair NodePair(InternalSourceNode, Node);
				Queue.Add(NodePair, FPipelineDataQueue(NodePair));
			}

			if (bIsExternalSyncNode) // Node is at the bottom of the graph - connect downstream to InternalSyncNode
			{
				DownstreamNodes[Node].Add(InternalSyncNode);
				UpstreamNodes[InternalSyncNode].Add(Node);

				TNodePair NodePair(Node, InternalSyncNode);
				Queue.Add(NodePair, FPipelineDataQueue(NodePair));
			}
		}
	}

	for (const FConnection& Connection : Connections)
	{
		if (!DownstreamNodes[Connection.From].Contains(Connection.To))
		{
			DownstreamNodes[Connection.From].Add(Connection.To);
		}

		if (!UpstreamNodes[Connection.To].Contains(Connection.From))
		{
			UpstreamNodes[Connection.To].Add(Connection.From);
		}

		TNodePair NodePair(Connection.From, Connection.To);
		if (!Queue.Contains(NodePair))
		{
			Queue.Add(NodePair, FPipelineDataQueue(NodePair));
		}
	}
}



// Process the pipeline using a single thread

void FPipelineProcess::PushSingleThreaded()
{
	MHA_CPUPROFILER_EVENT_SCOPE_STR("PushSingleThreaded");

	TArray<TSharedPtr<FNode>> WorkingNodes;

	UE_LOG(LogMetaHumanPipeline, Display, TEXT(""));
	UE_LOG(LogMetaHumanPipeline, Display, TEXT("Run start, single thread"));

	// Run node "Start" functions
	{
		MHA_CPUPROFILER_EVENT_SCOPE_STR("Start");

		UE_LOG(LogMetaHumanPipeline, VeryVerbose, TEXT(" Start"));

		for (const TSharedPtr<FNode>& Node : Nodes)
		{
			UE_LOG(LogMetaHumanPipeline, VeryVerbose, TEXT("  %s"), *Node->Name);

			StartNode(Node);

			if (bIsRunning)
			{
				WorkingNodes.Add(Node);
			}
			else
			{
				break;
			}
		}
	}

	// Run node "Process" functions if start did not error
	if (bIsRunning)
	{
		MHA_CPUPROFILER_EVENT_SCOPE_STR("Process");

		UE_LOG(LogMetaHumanPipeline, VeryVerbose, TEXT(" Process"));

		int32 LoopCounter = 0;

		while (bIsRunning && !IsAborted())
		{
			UE_LOG(LogMetaHumanPipeline, VeryVerbose, TEXT("  Loop %i - Producing %s"), LoopCounter++, bIsProducing ? TEXT("true") : TEXT("false"));

			bool bDoneWork = false;

			for (const TSharedPtr<FNode>& Node : WorkingNodes)
			{
				bDoneWork |= ProcessNode(Node);
			}

			if (!bDoneWork)
			{
				FPlatformProcess::Sleep(0.001f);
			}
		}
	}

	// Run node "End" functions
	{
		MHA_CPUPROFILER_EVENT_SCOPE_STR("End");

		UE_LOG(LogMetaHumanPipeline, VeryVerbose, TEXT(" End"));

		for (const TSharedPtr<FNode>& Node : WorkingNodes)
		{
			UE_LOG(LogMetaHumanPipeline, VeryVerbose, TEXT("  %s"), *Node->Name);

			EndNode(Node);
		}
	}

	UE_LOG(LogMetaHumanPipeline, Display, TEXT("Run end"));
}



// Process the pipeline using a thread per node

void FPipelineProcess::PushThreadPerNode()
{
	MHA_CPUPROFILER_EVENT_SCOPE_STR("PushThreadPerNode");

	UE_LOG(LogMetaHumanPipeline, Display, TEXT(""));
	UE_LOG(LogMetaHumanPipeline, Display, TEXT("Run start, thread per node"));

	// Create a task to process each node
	// If we are a synchronous run, then ensure the InternalSyncNode is run synchronously in the calling thread (the game thread)
	// so that its messages, eg frame complete, are correctly routed (delivered directly to the game thread rather than a task 
	// destined for the game thread)

	TArray<UE::Geometry::FAsyncTaskExecuterWithAbort<FPipelineNodeProcess>*> Processes;
	Processes.Reserve(Nodes.Num());

	UE::Geometry::FAsyncTaskExecuterWithAbort<FPipelineNodeProcess>* InternalSyncNodeProcess = nullptr;

	for (const TSharedPtr<FNode>& Node : Nodes)
	{
		UE::Geometry::FAsyncTaskExecuterWithAbort<FPipelineNodeProcess>* Process = new UE::Geometry::FAsyncTaskExecuterWithAbort<FPipelineNodeProcess>(Node, this);
		Process->GetTask().SetAbortSource(&TaskWrapper->bAbort);

		if (Node == InternalSyncNode && PipelineRunParameters.GetMode() == UE::MetaHuman::Pipeline::EPipelineMode::PushSyncNodes)
		{
			InternalSyncNodeProcess = Process;
		}
		else
		{
			Processes.Add(Process);
			Process->StartBackgroundTask();
		}
	}

	if (InternalSyncNodeProcess)
	{
		InternalSyncNodeProcess->StartSynchronousTask();
	}

	// Wait for each task to complete

	for (auto Process : Processes)
	{
		Process->EnsureCompletion();
		delete Process;
	}

	UE_LOG(LogMetaHumanPipeline, Display, TEXT("Run end"));
}



FPipelineNodeProcess::FPipelineNodeProcess(const TSharedPtr<FNode>& InNode, FPipelineProcess* InPipelineProcess)
	: Node(InNode), PipelineProcess(InPipelineProcess)
{
}

void FPipelineNodeProcess::DoWork()
{
	MHA_CPUPROFILER_EVENT_SCOPE_TEXT(*Node->Name);

	// Call the node's Start function
	{
		MHA_CPUPROFILER_EVENT_SCOPE_STR("Start");

		PipelineProcess->StartNode(Node);

		if (!PipelineProcess->IsRunning())
		{
			return;
		}
	}

	// Process the node 
	while (PipelineProcess->IsRunning() && !IsAborted())
	{
		if (!PipelineProcess->ProcessNode(Node))
		{
			FPlatformProcess::Sleep(0.001f);
		}
	}

	// Call the node's End function
	{
		MHA_CPUPROFILER_EVENT_SCOPE_STR("End");

		PipelineProcess->EndNode(Node);
	}
}

}

